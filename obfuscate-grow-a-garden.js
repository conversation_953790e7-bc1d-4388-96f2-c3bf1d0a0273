const fs = require('fs');
const luamin = require('./luamin.js');

// Read the original grow-a-garden1.lua file
console.log('Reading grow-a-garden1.lua...');
const originalCode = fs.readFileSync('grow-a-garden1.lua', 'utf8');

console.log('Original file size:', originalCode.length, 'characters');
console.log('Starting obfuscation...');

try {
    // Apply basic minification first
    const minified = luamin.minify(originalCode, {
        renameFunctions: true,
        renameGlobals: false
    });
    
    console.log('Minified size:', minified.length, 'characters');
    console.log('Compression ratio:', ((originalCode.length - minified.length) / originalCode.length * 100).toFixed(2) + '%');
    
    // Save the obfuscated version
    fs.writeFileSync('grow-a-garden1-obfuscated.lua', minified);
    console.log('✅ Obfuscated file saved as: grow-a-garden1-obfuscated.lua');
    
    // Also create a backup with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `grow-a-garden1-obfuscated-${timestamp}.lua`;
    fs.writeFileSync(backupName, minified);
    console.log('📁 Backup saved as:', backupName);
    
} catch (error) {
    console.error('❌ Error during obfuscation:', error.message);
    console.error('Stack trace:', error.stack);
}
