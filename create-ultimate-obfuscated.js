const fs = require('fs');
const luamin = require('./luamin.js');

// Read the original grow-a-garden1.lua file
console.log('Reading grow-a-garden1.lua...');
const originalCode = fs.readFileSync('grow-a-garden1.lua', 'utf8');

console.log('Original file size:', originalCode.length, 'characters');
console.log('Creating ultimate obfuscated version...');

// Enhanced obfuscation with multiple layers
function createUltimateObfuscation(code) {
    try {
        // Step 1: Basic minification
        const minified = luamin.minify(code, {
            renameFunctions: true,
            renameGlobals: false
        });
        
        // Step 2: Create string table with Base64 encoding
        const strings = [];
        const stringMap = new Map();
        
        // Extract string literals
        const stringRegex = /"([^"\\]*(\\.[^"\\]*)*)"/g;
        let match;
        let stringIndex = 0;
        
        while ((match = stringRegex.exec(minified)) !== null) {
            const originalString = match[0];
            const stringContent = match[1];
            
            if (!stringMap.has(originalString)) {
                // Encode string to Base64 with some obfuscation
                const encoded = Buffer.from(stringContent).toString('base64');
                // Add some random characters to make it harder to decode
                const obfuscated = encoded.split('').map((char, i) => {
                    if (i % 3 === 0 && Math.random() > 0.7) {
                        return char + ['!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '_', '+', '=', '[', ']', '{', '}', '|', '\\', ':', ';', '"', "'", '<', '>', ',', '.', '?', '/'][Math.floor(Math.random() * 28)];
                    }
                    return char;
                }).join('');
                
                strings.push(obfuscated);
                stringMap.set(originalString, stringIndex);
                stringIndex++;
            }
        }
        
        // Step 3: Replace strings with table references
        let obfuscatedCode = minified;
        for (const [originalString, index] of stringMap) {
            const regex = new RegExp(originalString.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
            obfuscatedCode = obfuscatedCode.replace(regex, `atMZsVpq(TgjuaeTTO[${index + 1}])`);
        }
        
        // Step 4: Create the decoder function with random variable names
        const randomVarNames = {
            stringTable: 'TgjuaeTTO',
            cleanBase64: 'bGclHhpSB',
            decodeBase64: 'OrNMCHJxDtpVp',
            mainDecoder: 'atMZsVpq',
            utilityFunction: 'mkLZEDNWUWi'
        };
        
        // Step 5: Generate the final obfuscated code
        const finalCode = `local ${randomVarNames.stringTable}={${strings.map(s => `"${s}"`).join(',')}};` +
            `local function ${randomVarNames.cleanBase64}(ESErlnlRs)` +
            `local uIuSJzSrsWlMf="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="` +
            `local XTxEcHrTxE=""` +
            `for zVsRAvNlrEjEp=1,#ESErlnlRs do ` +
            `local mnFeZjpQXdFXC=ESErlnlRs:sub(zVsRAvNlrEjEp,zVsRAvNlrEjEp)` +
            `if uIuSJzSrsWlMf:find(mnFeZjpQXdFXC,1,true)then XTxEcHrTxE=XTxEcHrTxE..mnFeZjpQXdFXC end end ` +
            `return XTxEcHrTxE end;` +
            `local function ${randomVarNames.decodeBase64}(lGQeYmQI)` +
            `local hHkRPGOYT="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"` +
            `local aGaDPFQtnQ={}` +
            `for XVYEdWdjzUdFp=1,#hHkRPGOYT do aGaDPFQtnQ[hHkRPGOYT:sub(XVYEdWdjzUdFp,XVYEdWdjzUdFp)]=XVYEdWdjzUdFp-1 end ` +
            `local QZzZXxLAQoOJ=""` +
            `for SIMxtxmogj=1,#lGQeYmQI,4 do ` +
            `local dfYpUFrjQUhVA,bahCSPGBZXXN,NucIosUzvikIZ,KCxSNvjTJ=aGaDPFQtnQ[lGQeYmQI:sub(SIMxtxmogj,SIMxtxmogj)],aGaDPFQtnQ[lGQeYmQI:sub(SIMxtxmogj+1,SIMxtxmogj+1)],aGaDPFQtnQ[lGQeYmQI:sub(SIMxtxmogj+2,SIMxtxmogj+2)],aGaDPFQtnQ[lGQeYmQI:sub(SIMxtxmogj+3,SIMxtxmogj+3)]` +
            `if dfYpUFrjQUhVA and bahCSPGBZXXN then ` +
            `local cAtqjFwzu=dfYpUFrjQUhVA*4+math.floor(bahCSPGBZXXN/16)` +
            `QZzZXxLAQoOJ=QZzZXxLAQoOJ..string.char(cAtqjFwzu)` +
            `if NucIosUzvikIZ then cAtqjFwzu=math.fmod(bahCSPGBZXXN,16)*16+math.floor(NucIosUzvikIZ/4)` +
            `QZzZXxLAQoOJ=QZzZXxLAQoOJ..string.char(cAtqjFwzu)end ` +
            `if KCxSNvjTJ then cAtqjFwzu=math.fmod(NucIosUzvikIZ,4)*64+KCxSNvjTJ ` +
            `QZzZXxLAQoOJ=QZzZXxLAQoOJ..string.char(cAtqjFwzu)end end end ` +
            `return QZzZXxLAQoOJ end;` +
            `local function ${randomVarNames.mainDecoder}(s)return ${randomVarNames.decodeBase64}(${randomVarNames.cleanBase64}(s))end;` +
            `local function ${randomVarNames.utilityFunction}(id,...)` +
            `if id==1 then return type(...)` +
            `elseif id==2 then return tostring(...)` +
            `elseif id==3 then return table.insert(...)` +
            `elseif id==4 then return pairs(...)` +
            `elseif id==5 then return tonumber(...)end end;` +
            obfuscatedCode;
        
        return finalCode;
        
    } catch (error) {
        console.error('Error during obfuscation:', error);
        return null;
    }
}

const ultimateObfuscated = createUltimateObfuscation(originalCode);

if (ultimateObfuscated) {
    console.log('Ultimate obfuscated size:', ultimateObfuscated.length, 'characters');
    console.log('Compression ratio:', ((originalCode.length - ultimateObfuscated.length) / originalCode.length * 100).toFixed(2) + '%');
    
    // Save the ultimate obfuscated version
    fs.writeFileSync('grow-a-garden1-ULTIMATE-obfuscated.lua', ultimateObfuscated);
    console.log('✅ Ultimate obfuscated file saved as: grow-a-garden1-ULTIMATE-obfuscated.lua');
    
    // Also create a backup with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `grow-a-garden1-ULTIMATE-${timestamp}.lua`;
    fs.writeFileSync(backupName, ultimateObfuscated);
    console.log('📁 Ultimate backup saved as:', backupName);
    
} else {
    console.error('❌ Ultimate obfuscation failed');
}
