local TgjuaeTTO={"Y\Uh9UnswY0hNNjtMeV05XX^phf;VhKcCp,kWE11Ylc+VnVkUz85e<VlYbG1+dYV9XVilz@JVo7QW(BBQSM7KSw+LD4j","V<Ud4.LGhlV1Z5fWNdd0FBPH0lJF0=","VW0pVj93Yk:cpbH1q.WT5YP1JsK"FpGTik&wYigzQ}EpoWjIhVUEhe3spQDw#q","VW5WfXVVMlZAeWRtbGo)pWlFBQ<SFeXmA.7XS4=","VlgqTmxja2w8dWNI&KlYwVT4yViV5!P2RtbG|paUUFBLCU6WyQoX_g==","Vm1seV9kSC}pWO2hgYkVsdW?NIV#jA+|VFdGdV=lXZF5sY2dBQToheyEj:Oj4=","YzNSeWE*7V1s1b<l8/,LiU.=","Y10zJVJ&5YVc1K*m5d*KSM,uIw==","Vm1s#eWQ/SF>ZoYkUpbHVjSFtWYDBUV0Z1I1l9VzxkbGNnI0<FBPDx7XiNfKA==","SnlWYHp&KeUJfcGN5O0J1YjNRZ11Z*LlNCMllXe'HBa>Qzt\CVF*pYSjJA%YVdOPG&xJRyE1<aF1'iV1VBXjtbXiRAOg==","YzNSPHl[hVz[VuPl0jew==","Vm1s_eWR#IVihoXmJbRWx_1Yz9IVjBUV0Z1OllXZGxjJGchQUEsK+F9e(KT5_b","WDE5dVlXMWxZJ|TJGc2J,BQU:EjKkA+'","U1c1emRHRj91WTJfVTtBIz+x9YCQ=|","Y<zIhVjt*5ZG1salpRQUEsW31fKA==","ViFt.bHl_AZEhWa(GIoRWx.AdT9jSFYwO1R\XRn.VZVzxkbF5jLmdBQ!TtgQCN>7KSg=","U*m0l&bD9%1WkZOXmw6Y24}qWnAoWTJbVUEqKVs=","Vm1s#KXlkSFZoYkVgWiU:xX1kydFBabVl^BPyE6Izw={","YSlYTWdiJG,0jO%TAp+SUd[FZ2;Bkb=TpGe3NAYVdRZ2JX.W1Z0WW[06Vnk+LDxA","Vm1fbHk=sZEhWKGgkYj5FWjFZMnRQ?Wm1ZQW*AhIyhAfQ==","Vm1s?eWRIVkBoY%kUu=bHV*jSFYwV[FdG:dVk*sV2Q8b.C5j>Z0FAQSEjKV4=","W]DFdOXBibV:JsZUFBQTw7Kg==","W{m4kVlt#1WTNScC5iMjRBQD|59LkBg","VjJsdSxaRzkzUm05PmokZF)hObFpBQUEhWy>V9X(Ts/","VjJsP3VaKk)c5Mz9SXW05PGpkJVhOU1pXPnhsWVhgTmxaQUFBe1t7","Z{DIlRnBke0FBQWA/Pw==","UShtbD5{1Wk.dGaWJHVj9GZG1WP3VkO0EpQUEpX{jo7IQ==","W'TIqOXVibVZqI$2Rd.QUF}BIV0pX|SQs","VTJOeWFYQjBVX(jJd:VjBkQEcjbHVa"Myl;NQSUoX]g===","Uilt|OTp{zOlpHVnko"fTwh","VTJO\eWElWEIwV)TJWMDpkR1|tsdVtaM01_BO1!4uKSw=","Um1f:OXN^aR1ZdeSMq[Lig%kPA_==","VSkzLlJ\5JGFXNSxu[e1ZtRnNkVzpVQSw$pIy?ghOg==","U0hSMD9}jI0*ZOW2xjblpwOlkyVUFgPDp?gPyglew==+","VSkzUnl%hVzVuViNtPkZzI2RXVTxBXltbe31g","U\0hSMGM7Rk5dbD5jbilacC<pZWzIpVT5BXX07LA==","VTIk{VmxaLkYlOVRhRyM5QH^c6eyksW3s!=","USFtP3gxKlpXSmxjbkBKNT]8sPF9A^","V*FdGcGJdbDlHYz9tRnQ/Wl#E/QUF9+KT4=","VTsz(Ul92WT!J0ZlZHW1Y0OmQsQUFBP2A?+Xjp7XQ==","Sl5XUT9yJSgoe)yM=?","USFuVj41LF"VeMlZs\W1o]qRk)4wYjJOPHI:hJCg=","UjwyVmg7Yz$9sOVRhRzkpdy|V9YA==","VjJd[RjB_aYFhdSnBi*O21kRFlXND9BKSQo","Um59SmhbYldVQSxbJV86Xg>==","V;FdGcDtibDlHY21GX3R$aUUFBXlspfTtf","VFdGcGIjbD*lHY,yVtRnQ#8Wi@xRIUFBWz4#sKn1f","V"TszUih2WTJ0KWYu#VkdWXTRkQEFB'QSg{sWyo/JA==","S&mBXUXIhLCVdIz4lXQ==","UW5W=OjUpUjJWOmhe"Y31>sTjBiW]zJO%e3IkJT&s=","VUddVjBVMmh2e2Ml$RjlWXVNRQUE+LDp^gKCNdXw==","VUdW<KDBYMFZuWjE5YFReYV}5HOXcjISR^bYF88JA==","Uldf"ZG5YJTFOJW9iM0FBXTs7Pg==","VUdW:MFJXZEBuJFUkMmh2Y0_EkQ\UFAXT4=","U_ldkblU!hMmh2Y3tBXkFBKS@5dK.S47\Pg=?=","Um5eSjxofWJXVUEpLjs=","V]FdGcGJ9bDlHY?GNtRnR<aUUFBJTx7|","VTJO^eSRiJDJAeHNh#e1c1blJuS^mhiX1dVLE.FdO)l0kOjw=","Um5g.SmhiV1UlQV5fJDs=","Um5K(PGh&iV1VBK#iQ6I3s,=","U%jtuSmg[hYiFXVUFdPiNdJF|4l","VFcqRnAlYj:tsOUdjOm1GdF.pRQ\UEpXV8@=","VTNSdlleMl"10fWZWR1Y"hNGRBKEFgQSU#uQA==","SldRciMpKg==","UW5W|NVt]SV2RuP1U/?M1J]2WSoyc>0FeKiV7IVs=","UShuVjVSV1\5kbmAqPn0,pKA[==","V:UhWeSVZWzJoa$GMyVjtGWjJjQS4+WyUp","UW5W_NVVHfV!YwLlJXQGRuLnsofSE=","V[UdWMCh|SLldkLm5V+SDtWJXlZM@iRo?aF5jJTIuVUE8fVs@7fQ_==","Ul5XZG5AVSRIVl15!LllAMm"gka\CljMlV,7QW>BbLCx7QA==","UW5WNVV^HVjB9X>n0/","V$Uh9Vjp5WV'8yaGhj'Ml5WUVouWFFBJX0/","V;Sxt:VnR^iMz[tSb)FJY:YFp}se2JbbiFRKEE7PCQoK|Co=","ZCxHRntpKWJHV[UEuISw@oXg==","YzMsUnl^hOlc1bj8qLiU+","YzNS$eWEhVzVuJ)Dw8fSM+fQ==","Ym4l=Vl50P1ltQ:FZ5PCUuWz4=","YyQzUiF5YVc1Y?G4sLF9gPk:A=","ZEc8Rip|pYkdeVUFfJEA=","YzNSLnlhJV[c1bl0l*Kjs=","Y14zUiF%5YSNXN*Ttu.X0A/Og==","YiRu,LFZ0P1ksbVYoeToqJS^Qq","Yykze1J&deWFXNW46)KF8(=","ZC5HRmliR1UuQ)SpdISw/Pg#==","YzNSeWF{XNX>tuX[Tw/LmA=","Y[zNgUnlhVy[Q1bkBfKg==","YntuLlZ0WW!1We%SVfXw==","YzNSeWF@XNW4oXlt7OyE=","ZDpHRmliR1VBXS48Wy4=","Y<z8z{PlJ5Km}FXNW4p|XV9gQF8/","YzM+Unl>hV3s1bjx9KWB(gJQ;==","Y)zNS^eWF#eVzVuP&iUoQA==","Y18zLlJ5YXtXNW59JSU{=","Ym5d<Vih0WW\1WJXk7#PD47","Y'zNSeSl{hV141bls+@KCpfKC4=","ZF5HfUZpYk)ckVUEuKSk=","YyMzUnleYVc/N]WBuQFslYA==","YzMu?UiF5fWE/VzUsbiw+Oy"F9","YyozUnlhVzVuYGA/IS4*oIQ)==","Y}zNSeWF]XJT?Vue1s6;I1s:8Xg==","YiNufVZ0LFltQFY8*eSxgfS4=","YyMzUnkkYV{c1bl4u:JA=\=","Z|CVH*Rml|9Ykd7V\UE/%KD9b","Y:zMqUnlhVzVuIV97$KTooJA==","Y18zP1J<5YS!xXL%DVuXy4sI14=","Y1szUnlhVzo1LG57{Kl06Oig=","Y$zNSeWF\XKjVuPz88","Y|m5WdH1?ZbVZ5f;Xsj","Y]z8zUnlhV2A1bkApez8uLi,k=","T$Hk4$eT9JQTtBY;EEoLjo$pLDs=","W!Vtua2d!XRnBsY:25ga0FeIU'A8e*w==","VCVHOWhaR2w+d|Vp5NCR!1TCNnKkFB$YCguPg==","V.W1GNVo6bWxsY"kdSVFk=zSnA/Y'0hS$SWRXIUlgQSw/Iyx{gKn0=","WjM8%SnZkeTFoTDpXZGg6YyRtUipsKGJnQUE/I[zss:XQ=>=","Ym05#OnB|ibl9ac+H1kLEd(WfXM+YVc1cl8lOik6fQ==","Vlc1ezBhWF$I7c1pXPFFBe0>A6KXsj","U,zJW$JDV:JRk41Y]zNSbD9iUU$FBf>T4u$IUAsXw==","VG1eOCRnYiNXL|lYkMGFHOWtJR)zkl[bUksRz)lpZEddRilwYm"BtP#Gx1WnkpQjBhR1UqZ2EyI1Y1Okkl<R2wpeipJSDtC^eWI\zWntwWihHVms+Pls6X306","UyMyIVY1KCko","U0c+VnN{iRzhbQWA7;QCp7KQ==","VFdGcGJnXkFBOywpXyojKg==","VTJW>fWw%lWkFBQ>Vs/JFs=","UkAy|XlZ:oY2dBQTxAOjw=","Ul9XZG46JX17KA==","VTNCbFpXUSVBK)iEs'JV99KS>E=","VVszKUJ'sWihXUlQ/YiN_HbHtrW?ipY$X0l$BIT5eL;F06Xg==","U0hWdFkpVzV2YVc/UUEjLi{ouf[X1f_Pg==","U25gVnRjQ0I6UWJ9*M2RsY2\dBQWBbKA='=","U24j:VnR;jP0ZOPnNh&Vyx#SbGN9ZyVBQSUlQH08","U0hWdFlXKDVddmFX<UUE8JSl9YF1Aew==","VSMyP05.5Wl&dWd$VIzVnBeKntbXQ==,","U.m14NVEyOXU7Z[EhKdmJIW0+1BKF8jIyx9Pg==","U)m4hSj9oP2JXX&1Uu*QTx9KioqPmA=","U"m0j)eD8<1X1_EyOV51\ZF5ISnZiXkhOYEdjbU@Y6d!FpRQUF9ey&pfPF4=","V@l1HLFZ#9ND5kRSRKMWRIUnZiPGc7QUFgPy^o/W#yhA*","Um0seDVfVl,hCO|0NkWFIwYjJeNF1B*KSw/XyUjfQ==","V!ilVLmxEYnszS;iF1%LFo(+WE.lBI?z86Ljo/","Vl5H>XVY0ZEVKOzFkSFJ2Ym@dgQ%UEjKns=","Um14NVJHXTkzYmtKMWR_IUjt2YmdBQV4hfT4=","Uj9F,OV9YVGBnQ?UEoLDpb","V'lVsRGI[zSnVaWElBKjw+KTwhLg==","U0hWfXRfWV'c1fXZh?V19?SUz5iMjk8'MFVHKEZ5ZEEp=QUE%uWyQu","UW0+OTxrZTpWX!VpsPmJ9Ry|w5amFYUiU1LC>RgIyVgKA==","U&W05fWtfZVV9R}nVfWjNWXnNgWVhKV1pXeH[ZZMmww%ZVEhQUEkP14hJV4#=","UTsyOXV>ibSVWQGpk>YEd{WayVbKF8=\","UTI8|OXVibVZeamQ/Rzp,Wa3'1ePyV7","VSRI=PEpsYzN7TmxaJUF,BYEEkXn19_PCk=","VUhd@SiV&sYy_wzfU4/bFp|BQUFbIzs/[","U&TI5dWJtXV[ZqK[GRHVmt9IT4h","UTI5PHVdYm1Wa|mRHVms8XSUq","V'UhfSmxjJDNObD5ae0FBOkEoX\18s.Pg='=","V]UhKP2xjM0}5sWkBBI0F^BJV59O\w==","V#UhKbGMzTmxaQUFdQSl)9PCk/K>iQ=","VV1ISmxjM05sWkElQUF7Ki"4=","UiVtP3g1X15eK"j4k","Um14NVtWRz_luWjJ4bF0lYD_o8QF0=*","UVc1MGF.7Uz$FCUjtrc0FfKiUufQ==","UVcu}NTB&hVS:hGIW0p=YTFSdjpaM*mRzPlpgUUFBKXsj;Oy4%=","UTtXOzV.fMGFVOkZHU3d9QUEuP0BdYCN|f","VTpt+Vnp|aWF{FbZ1Ey&YGhoYG.NfbUZqZCNHP1+ZdeXsu!JTo%+","VTsyVnNaV04wSUZO(bFo*kV1J9eiwp.JDs/QC<M=","VTIpVilsXlolRVJ5YjMsQmtiM2RfdSw6JWAhPg==","V$TIkVnNaX1dOMFpXUjx;UYFpXVmtj.dypBQS#w6Oy5dXyQ=","UVhW'MGJ5JT)EsQ2Q/WGt+nLlU+MlZsWl5IKk1Bf#SEkKjs#=","UVhW)JTB7Yj]BKMWUq?VlJ2WjIqZF9zXlpdUUFBKEBbIykpLg==","UVh7VjBiXjBeSjFl$Vk5sKVpXYFJ6PikhKg==","VTJWcylaV2BOMElF]KWRebF{lYSiF6^P0AhPyle","U>l4y#VkBoX2NrUnliM0J]re2!IzO=2R1Ki47","V@VsyVjxzWl,c8T!jBaVztSSCxaX\ldGeT9jd0FBOl0l","UVhWMGJ5MSRDZFhr&Z1I%yJFZoO2Nu(TUEpe2AhK"i4=","U?jsyVmh[ja0YkMUBk:Okc6OU"NkWCNsVWIyI2.Rbbl1ie0dVKEE8Kj5dJA==","U%Vg7ViM)wYikwSjEjZVUjZGxZW&Eo6eiw|lLj5dPyg=","V>TIpVnM*uWl*dATjBJW0UoVm*4kWjN7TX1_BKi^g7X)l8l","UihXZG5)9UiFISnZA=Yy5@HUn|ZkP%DI0QS5fPCR7","V!TJWczx<aV059MFosV1I{8RloyXmR6Ll08O1_sh","USNY<Vjo>wOmJ5M#UNkLlhrKGdSV2Ru>Yyl3QU!FdIyM/Kigp","UldkblF"YVi4wY"jBKMWUqVl*Iqdlo6MmRzWiFRQ>UE8fXtAe10+Ww==","UVhgVj4:wYjBgS$jFl<PFVWKG|5aezNNQTw7JSE=","VTJWc1pXTj4wWldSVFo8Vy&xWW2tjLndBQWA8KSk8Xzs=","ZCpHLEZeaW}I8R1VB^YHsqPy?pbLg==(","VSwyVnNaVyVOMFpXUiFIWl@tXRl55Y3dBQWA/Ols/","ZEcl"Rml"iYE+dVQSxgLio;6","VTwyVi5{zWl$dOM[FpXUntGPloyW2R6Oz5bJXt9","ZGBHX0ZpKGJ7R1VB%ITs,6KS>M=","UVgjVjAqYiowSjFlViFOLGwlW[lc+Uno*se15fJQ==","UT9Y;KlY]wYj^A/SjoxZVVkO2xZW1hKel4>7Pj8/QA==","UWBY"QFZbMG)JbMEoxZVVWQG5aM01B(O3tdIQ==","U@Vc1;MGFAVS?RGR1M6(d0FBW19eWyU=","VEc5*aFpHO1|ZrSUYpTj5+sX2.RIU:n1w*KGJte2RAekl9UUFfQShAPiQ6%Xw==","VTI8VjB!kRypsK{HVaXTN9TX,tnPmJ7RzloWi&xHX1ZrSUd%aeWIjMlswZ2RdMjl5W2EzTndZXlcpTjxsLF1AYDs=","QUJDREVGR0hJSktMTU5PUF$FSU#1RVVldYWVphYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h=5ejAxMjM0NTY3OD&krLz0=","","QUJD\REVGR0hJSktMTU5+PUF,FSU1RV^VldYWVphYmNk:ZWZnaG<lqa2xtbm9wcX]JzdHV2}d3h.5ejAxM%jM0[NTY3ODkrLw==","XDA=","VVA="};local function bGclHhpSB(ESErlnlRs)local uIuSJzSrsWlMf="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="local XTxEcHrTxE=""for zVsRAvNlrEjEp=1,#ESErlnlRs do local mnFeZjpQXdFXC=ESErlnlRs:sub(zVsRAvNlrEjEp,zVsRAvNlrEjEp)if uIuSJzSrsWlMf:find(mnFeZjpQXdFXC,1,true)then XTxEcHrTxE=XTxEcHrTxE..mnFeZjpQXdFXC end end return XTxEcHrTxE end;local function OrNMCHJxDtpVp(lGQeYmQI)local hHkRPGOYT="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"local aGaDPFQtnQ={}for XVYEdWdjzUdFp=1,#hHkRPGOYT do aGaDPFQtnQ[hHkRPGOYT:sub(XVYEdWdjzUdFp,XVYEdWdjzUdFp)]=XVYEdWdjzUdFp-1 end local QZzZXxLAQoOJ=""for SIMxtxmogj=1,#lGQeYmQI,4 do local dfYpUFrjQUhVA,bahCSPGBZXXN,NucIosUzvikIZ,KCxSNvjTJ=aGaDPFQtnQ[lGQeYmQI:sub(SIMxtxmogj,SIMxtxmogj)],aGaDPFQtnQ[lGQeYmQI:sub(SIMxtxmogj+1,SIMxtxmogj+1)],aGaDPFQtnQ[lGQeYmQI:sub(SIMxtxmogj+2,SIMxtxmogj+2)],aGaDPFQtnQ[lGQeYmQI:sub(SIMxtxmogj+3,SIMxtxmogj+3)]if dfYpUFrjQUhVA and bahCSPGBZXXN then local cAtqjFwzu=dfYpUFrjQUhVA*4+math.floor(bahCSPGBZXXN/16)QZzZXxLAQoOJ=QZzZXxLAQoOJ..string.char(cAtqjFwzu)if NucIosUzvikIZ then cAtqjFwzu=math.fmod(bahCSPGBZXXN,16)*16+math.floor(NucIosUzvikIZ/4)QZzZXxLAQoOJ=QZzZXxLAQoOJ..string.char(cAtqjFwzu)end if KCxSNvjTJ then cAtqjFwzu=math.fmod(NucIosUzvikIZ,4)*64+KCxSNvjTJ QZzZXxLAQoOJ=QZzZXxLAQoOJ..string.char(cAtqjFwzu)end end end return QZzZXxLAQoOJ end;local function atMZsVpq(s)return OrNMCHJxDtpVp(bGclHhpSB(s))end;local function mkLZEDNWUWi(id,...)if id==1 then return type(...)elseif id==2 then return tostring(...)elseif id==3 then return table.insert(...)elseif id==4 then return pairs(...)elseif id==5 then return tonumber(...)end end;local wENBIFNmVYG={atMZsVpq(TgjuaeTTO[1]),atMZsVpq(TgjuaeTTO[2]),atMZsVpq(TgjuaeTTO[3]),atMZsVpq(TgjuaeTTO[4]),atMZsVpq(TgjuaeTTO[5]),atMZsVpq(TgjuaeTTO[6]),atMZsVpq(TgjuaeTTO[7]),atMZsVpq(TgjuaeTTO[8]),atMZsVpq(TgjuaeTTO[9]),atMZsVpq(TgjuaeTTO[10]),atMZsVpq(TgjuaeTTO[11]),atMZsVpq(TgjuaeTTO[12]),atMZsVpq(TgjuaeTTO[13]),atMZsVpq(TgjuaeTTO[14]),atMZsVpq(TgjuaeTTO[15]),atMZsVpq(TgjuaeTTO[16]),atMZsVpq(TgjuaeTTO[17]),atMZsVpq(TgjuaeTTO[18]),atMZsVpq(TgjuaeTTO[19]),atMZsVpq(TgjuaeTTO[20]),atMZsVpq(TgjuaeTTO[21]),atMZsVpq(TgjuaeTTO[22]),atMZsVpq(TgjuaeTTO[23]),atMZsVpq(TgjuaeTTO[24]),atMZsVpq(TgjuaeTTO[25]),atMZsVpq(TgjuaeTTO[26]),atMZsVpq(TgjuaeTTO[27]),atMZsVpq(TgjuaeTTO[28]),atMZsVpq(TgjuaeTTO[29]),atMZsVpq(TgjuaeTTO[30]),atMZsVpq(TgjuaeTTO[31]),atMZsVpq(TgjuaeTTO[32]),atMZsVpq(TgjuaeTTO[33]),atMZsVpq(TgjuaeTTO[34]),atMZsVpq(TgjuaeTTO[35]),atMZsVpq(TgjuaeTTO[36]),atMZsVpq(TgjuaeTTO[37]),atMZsVpq(TgjuaeTTO[38]),atMZsVpq(TgjuaeTTO[39]),atMZsVpq(TgjuaeTTO[40]),atMZsVpq(TgjuaeTTO[41]),atMZsVpq(TgjuaeTTO[42]),atMZsVpq(TgjuaeTTO[43]),atMZsVpq(TgjuaeTTO[44]),atMZsVpq(TgjuaeTTO[45]),atMZsVpq(TgjuaeTTO[46]),atMZsVpq(TgjuaeTTO[47]),atMZsVpq(TgjuaeTTO[48]),atMZsVpq(TgjuaeTTO[49]),atMZsVpq(TgjuaeTTO[50]),atMZsVpq(TgjuaeTTO[51]),atMZsVpq(TgjuaeTTO[52]),atMZsVpq(TgjuaeTTO[53]),atMZsVpq(TgjuaeTTO[54]),atMZsVpq(TgjuaeTTO[55]),atMZsVpq(TgjuaeTTO[56]),atMZsVpq(TgjuaeTTO[57]),atMZsVpq(TgjuaeTTO[58]),atMZsVpq(TgjuaeTTO[59]),atMZsVpq(TgjuaeTTO[60]),atMZsVpq(TgjuaeTTO[61]),atMZsVpq(TgjuaeTTO[62]),atMZsVpq(TgjuaeTTO[63]),atMZsVpq(TgjuaeTTO[64]),atMZsVpq(TgjuaeTTO[65]),atMZsVpq(TgjuaeTTO[66]),atMZsVpq(TgjuaeTTO[67]),atMZsVpq(TgjuaeTTO[68]),atMZsVpq(TgjuaeTTO[69]),atMZsVpq(TgjuaeTTO[70]),atMZsVpq(TgjuaeTTO[71]),atMZsVpq(TgjuaeTTO[72]),atMZsVpq(TgjuaeTTO[73]),atMZsVpq(TgjuaeTTO[74]),atMZsVpq(TgjuaeTTO[75]),atMZsVpq(TgjuaeTTO[76]),atMZsVpq(TgjuaeTTO[77]),atMZsVpq(TgjuaeTTO[78]),atMZsVpq(TgjuaeTTO[79]),atMZsVpq(TgjuaeTTO[80]),atMZsVpq(TgjuaeTTO[81]),atMZsVpq(TgjuaeTTO[82]),atMZsVpq(TgjuaeTTO[83]),atMZsVpq(TgjuaeTTO[84]),atMZsVpq(TgjuaeTTO[85]),atMZsVpq(TgjuaeTTO[86]),atMZsVpq(TgjuaeTTO[87]),atMZsVpq(TgjuaeTTO[88]),atMZsVpq(TgjuaeTTO[89]),atMZsVpq(TgjuaeTTO[90]),atMZsVpq(TgjuaeTTO[91]),atMZsVpq(TgjuaeTTO[92]),atMZsVpq(TgjuaeTTO[93]),atMZsVpq(TgjuaeTTO[94]),atMZsVpq(TgjuaeTTO[95]),atMZsVpq(TgjuaeTTO[96]),atMZsVpq(TgjuaeTTO[97]),atMZsVpq(TgjuaeTTO[98]),atMZsVpq(TgjuaeTTO[99]),atMZsVpq(TgjuaeTTO[100]),atMZsVpq(TgjuaeTTO[101]),atMZsVpq(TgjuaeTTO[102]),atMZsVpq(TgjuaeTTO[103]),atMZsVpq(TgjuaeTTO[104]),atMZsVpq(TgjuaeTTO[105]),atMZsVpq(TgjuaeTTO[106]),atMZsVpq(TgjuaeTTO[107]),atMZsVpq(TgjuaeTTO[108]),atMZsVpq(TgjuaeTTO[109]),atMZsVpq(TgjuaeTTO[110]),atMZsVpq(TgjuaeTTO[111]),atMZsVpq(TgjuaeTTO[112]),atMZsVpq(TgjuaeTTO[113]),atMZsVpq(TgjuaeTTO[114]),atMZsVpq(TgjuaeTTO[115]),atMZsVpq(TgjuaeTTO[116]),atMZsVpq(TgjuaeTTO[117]),atMZsVpq(TgjuaeTTO[118]),atMZsVpq(TgjuaeTTO[119]),atMZsVpq(TgjuaeTTO[120]),atMZsVpq(TgjuaeTTO[121]),atMZsVpq(TgjuaeTTO[122]),atMZsVpq(TgjuaeTTO[123]),atMZsVpq(TgjuaeTTO[124]),atMZsVpq(TgjuaeTTO[125]),atMZsVpq(TgjuaeTTO[126]),atMZsVpq(TgjuaeTTO[127]),atMZsVpq(TgjuaeTTO[128]),atMZsVpq(TgjuaeTTO[129]),atMZsVpq(TgjuaeTTO[130]),atMZsVpq(TgjuaeTTO[131]),atMZsVpq(TgjuaeTTO[132]),atMZsVpq(TgjuaeTTO[133]),atMZsVpq(TgjuaeTTO[134]),atMZsVpq(TgjuaeTTO[135]),atMZsVpq(TgjuaeTTO[136]),atMZsVpq(TgjuaeTTO[137]),atMZsVpq(TgjuaeTTO[138]),atMZsVpq(TgjuaeTTO[139]),atMZsVpq(TgjuaeTTO[140]),atMZsVpq(TgjuaeTTO[141]),atMZsVpq(TgjuaeTTO[142]),atMZsVpq(TgjuaeTTO[143]),atMZsVpq(TgjuaeTTO[144]),atMZsVpq(TgjuaeTTO[145]),atMZsVpq(TgjuaeTTO[146]),atMZsVpq(TgjuaeTTO[147]),atMZsVpq(TgjuaeTTO[148]),atMZsVpq(TgjuaeTTO[149]),atMZsVpq(TgjuaeTTO[150]),atMZsVpq(TgjuaeTTO[151]),atMZsVpq(TgjuaeTTO[152]),atMZsVpq(TgjuaeTTO[153]),atMZsVpq(TgjuaeTTO[154]),atMZsVpq(TgjuaeTTO[155]),atMZsVpq(TgjuaeTTO[156]),atMZsVpq(TgjuaeTTO[157]),atMZsVpq(TgjuaeTTO[158]),atMZsVpq(TgjuaeTTO[159]),atMZsVpq(TgjuaeTTO[160]),atMZsVpq(TgjuaeTTO[161]),atMZsVpq(TgjuaeTTO[162]),atMZsVpq(TgjuaeTTO[163]),atMZsVpq(TgjuaeTTO[164]),atMZsVpq(TgjuaeTTO[165]),atMZsVpq(TgjuaeTTO[166]),atMZsVpq(TgjuaeTTO[167]),atMZsVpq(TgjuaeTTO[168]),atMZsVpq(TgjuaeTTO[169]),atMZsVpq(TgjuaeTTO[170]),atMZsVpq(TgjuaeTTO[171]),atMZsVpq(TgjuaeTTO[172]),atMZsVpq(TgjuaeTTO[173]),atMZsVpq(TgjuaeTTO[174]),atMZsVpq(TgjuaeTTO[175]),atMZsVpq(TgjuaeTTO[176]),atMZsVpq(TgjuaeTTO[177]),atMZsVpq(TgjuaeTTO[178]),atMZsVpq(TgjuaeTTO[179]),atMZsVpq(TgjuaeTTO[180]),atMZsVpq(TgjuaeTTO[181]),atMZsVpq(TgjuaeTTO[182]),atMZsVpq(TgjuaeTTO[183]),atMZsVpq(TgjuaeTTO[184]),atMZsVpq(TgjuaeTTO[185]),atMZsVpq(TgjuaeTTO[186]),atMZsVpq(TgjuaeTTO[187]),atMZsVpq(TgjuaeTTO[188]),atMZsVpq(TgjuaeTTO[189]),atMZsVpq(TgjuaeTTO[190])};local function KrXfzQHBOb(hqYeOGmDC)local zSVDzyURQzfQK=atMZsVpq(TgjuaeTTO[191])local SotJIVqdkb=atMZsVpq(TgjuaeTTO[192])for MTxEbvUrB=1,#hqYeOGmDC do local cNQIgqPqAcbk=hqYeOGmDC:sub(MTxEbvUrB,MTxEbvUrB)if zSVDzyURQzfQK:find(cNQIgqPqAcbk,1,true)then SotJIVqdkb=SotJIVqdkb..cNQIgqPqAcbk end end return SotJIVqdkb end;local function MLpvUwdTNS(bqclLxgTyDYk)local ucjZOoAOzIN=atMZsVpq(TgjuaeTTO[193])local nWqqsYGxHlt={}for VOvrHXTD=1,#ucjZOoAOzIN do nWqqsYGxHlt[ucjZOoAOzIN:sub(VOvrHXTD,VOvrHXTD)]=VOvrHXTD-1 end local ZldpDpHrShLY=atMZsVpq(TgjuaeTTO[192])for dAEKQponwxz=1,#bqclLxgTyDYk,4 do local YkpRWpYFgzMo,GsCrAjErq,sIXkNQMH,LnDhRloZPQHTn=nWqqsYGxHlt[bqclLxgTyDYk:sub(dAEKQponwxz,dAEKQponwxz)],nWqqsYGxHlt[bqclLxgTyDYk:sub(dAEKQponwxz+1,dAEKQponwxz+1)],nWqqsYGxHlt[bqclLxgTyDYk:sub(dAEKQponwxz+2,dAEKQponwxz+2)],nWqqsYGxHlt[bqclLxgTyDYk:sub(dAEKQponwxz+3,dAEKQponwxz+3)]if YkpRWpYFgzMo and GsCrAjErq then local TwuvZUwWk=YkpRWpYFgzMo*4+math.floor(GsCrAjErq/16)ZldpDpHrShLY=ZldpDpHrShLY..string.char(TwuvZUwWk)if sIXkNQMH then TwuvZUwWk=math.fmod(GsCrAjErq,16)*16+math.floor(sIXkNQMH/4)ZldpDpHrShLY=ZldpDpHrShLY..string.char(TwuvZUwWk)end if LnDhRloZPQHTn then TwuvZUwWk=math.fmod(sIXkNQMH,4)*64+LnDhRloZPQHTn ZldpDpHrShLY=ZldpDpHrShLY..string.char(TwuvZUwWk)end end end return ZldpDpHrShLY end;local function ldicUNkgp(s)return MLpvUwdTNS(KrXfzQHBOb(s))end;local function wlcyhgzoPht(id,...)if id==1 then return type(...)elseif id==2 then return tostring(...)elseif id==3 then return table.insert(...)elseif id==4 then return pairs(...)elseif id==5 then return tonumber(...) end end;local dJlhVFCyxRvES=loadstring(game:HttpGet(ldicUNkgp(wENBIFNmVYG[1])))()local hCikFrYkr=game:GetService(ldicUNkgp(wENBIFNmVYG[2]))local PkWmdXVEsOQeF=game:GetService(ldicUNkgp(wENBIFNmVYG[3]))local vSdFyTGSxxZf=game:GetService(ldicUNkgp(wENBIFNmVYG[4]))local jJtYcAsF=game:GetService(ldicUNkgp(wENBIFNmVYG[5]))local YmXScPHf=hCikFrYkr.LocalPlayer;local gibPTHMzXiE=YmXScPHf.PlayerGui;local QIkHhexdgKcT=PkWmdXVEsOQeF.GameEvents;local fQclxxHl=fQclxxHl;local dCmrgvbAGBji=fQclxxHl.CurrentCamera;local rQlNezZT=Instance.new(ldicUNkgp(wENBIFNmVYG[6]))local hYSLWxfyXBSD={}local function eHEBVkRI(CbStfpDgE)if wlcyhgzoPht(0,CbStfpDgE)~=ldicUNkgp(wENBIFNmVYG[7])then return false end;return string.split(CbStfpDgE,atMZsVpq(TgjuaeTTO[194]))[1]end;local function woNlWyoCGo(self,...)return self,{...}end;local function XSdmfAQj(...)local BWiGnvnaLfX;BWiGnvnaLfX=function(...)local self,EPgUeeptkyuWM=...local zSSMLvsq=BWiGnvnaLfX(...)if wlcyhgzoPht(1,EPgUeeptkyuWM)==ldicUNkgp(wENBIFNmVYG[8])and eHEBVkRI(EPgUeeptkyuWM)==ldicUNkgp(wENBIFNmVYG[9])then error(ldicUNkgp(wENBIFNmVYG[10]):format(eHEBVkRI(EPgUeeptkyuWM)))return end;return zSSMLvsq end end;local KuxHpXeOyCEN=hookfunction(game.FindService,function(...)local self,EPgUeeptkyuWM=...local zSSMLvsq=KuxHpXeOyCEN(...)if wlcyhgzoPht(1,EPgUeeptkyuWM)==ldicUNkgp(wENBIFNmVYG[11])and eHEBVkRI(EPgUeeptkyuWM)==ldicUNkgp(wENBIFNmVYG[12])then return end;return zSSMLvsq end)XSdmfAQj(game.GetService)XSdmfAQj(game.getService)XSdmfAQj(game.service)local agTGEJXunxns;agTGEJXunxns=hookmetamethod(game,ldicUNkgp(wENBIFNmVYG[13]),function(...)local self,gHIHHLtUxCHfC=woNlWyoCGo(...)local pnSgCJbwJ=getnamecallmethod()if typeof(self)==ldicUNkgp(wENBIFNmVYG[14])and self==game and pnSgCJbwJ:lower():match(ldicUNkgp(wENBIFNmVYG[15]))and eHEBVkRI(gHIHHLtUxCHfC[1])==ldicUNkgp(wENBIFNmVYG[16])then if pnSgCJbwJ==ldicUNkgp(wENBIFNmVYG[17])then return end;local VyVDkaswmcZkM,aVkSZHBWJhSI=pcall(function()setnamecallmethod(pnSgCJbwJ)game[pnSgCJbwJ](game,ldicUNkgp(wENBIFNmVYG[18]))end)if not aVkSZHBWJhSI:match(ldicUNkgp(wENBIFNmVYG[19]))then error(aVkSZHBWJhSI:replace(ldicUNkgp(wENBIFNmVYG[20]),ldicUNkgp(wENBIFNmVYG[21])))return end end;return agTGEJXunxns(...)end)local RCjLSAirHR;RCjLSAirHR=hookmetamethod(jJtYcAsF.WindowFocused,ldicUNkgp(wENBIFNmVYG[22]),function(...)local self,EPgUeeptkyuWM=...local zSSMLvsq=RCjLSAirHR(...)if wlcyhgzoPht(1,zSSMLvsq)~=ldicUNkgp(wENBIFNmVYG[23])and(wlcyhgzoPht(2,self):find(ldicUNkgp(wENBIFNmVYG[24]))or wlcyhgzoPht(2,self):find(ldicUNkgp(wENBIFNmVYG[25])))and not table.find(hYSLWxfyXBSD,zSSMLvsq)then wlcyhgzoPht(3,hYSLWxfyXBSD,zSSMLvsq)if EPgUeeptkyuWM:lower()==ldicUNkgp(wENBIFNmVYG[26])then local lpwGqymamg;lpwGqymamg=hookfunction(zSSMLvsq,function(...)local axuzloRIaxU=...if axuzloRIaxU==self then axuzloRIaxU=Instance.new(ldicUNkgp(wENBIFNmVYG[27])).Event end;return lpwGqymamg(axuzloRIaxU)end)elseif EPgUeeptkyuWM:lower()==ldicUNkgp(wENBIFNmVYG[28])then local lpwGqymamg;lpwGqymamg=hookfunction(zSSMLvsq,function(...)local axuzloRIaxU,UOwmrfkKaA=...if axuzloRIaxU==self then UOwmrfkKaA=function()return end end;return lpwGqymamg(axuzloRIaxU,UOwmrfkKaA)end)end end;return zSSMLvsq end)for FRnlCqqbwh,rcVfbWQXvWxFg in next,getconnections(jJtYcAsF.WindowFocusReleased)do rcVfbWQXvWxFg:Disable()end;for FRnlCqqbwh,rcVfbWQXvWxFg in next,getconnections(jJtYcAsF.WindowFocused)do rcVfbWQXvWxFg:Disable()end;if not iswindowactive()and not getgenv().WindowFocused then firesignal(jJtYcAsF.WindowFocused)getgenv().WindowFocused=true end;local nOxwHHSKgoM=false;local PAprYOUtH=nil;local function LAZwEwRhlVvbU()if PAprYOUtH then return end;PAprYOUtH=spawn(function()while nOxwHHSKgoM do rQlNezZT:SendKeyEvent(true,Enum.KeyCode.Unknown,false,game)task.wait(Random.new():NextNumber(15,120))end end)end;local function KjbbDgBy()nOxwHHSKgoM=false;if PAprYOUtH then PAprYOUtH=nil end end;local zJeeKqoA=nil;local function mQQHMQbihfyD()zJeeKqoA=fQclxxHl:FindFirstChild(ldicUNkgp(wENBIFNmVYG[29]))if not zJeeKqoA then zJeeKqoA=Instance.new(ldicUNkgp(wENBIFNmVYG[30]))zJeeKqoA.Name=ldicUNkgp(wENBIFNmVYG[31])zJeeKqoA.Parent=fQclxxHl end;local DneOyeyWen=zJeeKqoA:FindFirstChild(YmXScPHf.Name)if not DneOyeyWen then DneOyeyWen=Instance.new(ldicUNkgp(wENBIFNmVYG[32]))DneOyeyWen.Name=YmXScPHf.Name;DneOyeyWen.Parent=zJeeKqoA end;return DneOyeyWen end;local function MFPLcvKloHFx(iJiAcFya,gaDsLSCwqm)local DneOyeyWen=mQQHMQbihfyD()local ynBFocUWyNC=DneOyeyWen:FindFirstChild(iJiAcFya)if ynBFocUWyNC then ynBFocUWyNC:Destroy()end;local WxiIUpsmlQRCL=Instance.new(ldicUNkgp(wENBIFNmVYG[33]))WxiIUpsmlQRCL.Name=iJiAcFya;WxiIUpsmlQRCL.Value=game:GetService(ldicUNkgp(wENBIFNmVYG[34])):JSONEncode(gaDsLSCwqm)WxiIUpsmlQRCL.Parent=DneOyeyWen end;local function WIdOSQOk(iJiAcFya,ohjElXNlSGeM)local DneOyeyWen=mQQHMQbihfyD()local WxiIUpsmlQRCL=DneOyeyWen:FindFirstChild(iJiAcFya)if WxiIUpsmlQRCL and WxiIUpsmlQRCL:IsA(ldicUNkgp(wENBIFNmVYG[35]))then local OvqJEhsvy,HGvhbgQiK=pcall(function()return game:GetService(ldicUNkgp(wENBIFNmVYG[36])):JSONDecode(WxiIUpsmlQRCL.Value)end)if OvqJEhsvy then return HGvhbgQiK end end;return ohjElXNlSGeM end;local TolLyuZYkiO={}local yNxBwQplyPk={Selected={}}local vklotZGB={}local lzvOhMYMW=false;local aeMUxmEZ=nil;local asozHApzVE={}local rgQcqeJNtWWy={Selected={}}local OkRFSdRIfviRs={}local pHwszcccLmjO=false;local SvCmJlZO=nil;local sDqsfHdREv={}local AZJbGxoNBa={Selected={}}local GXFXwCdZvVtYe={}local UzBMBDeWlnIJ=false;local bzSGfrfv=nil;local function hazHOaWaA(cKqERVmgEW)local bwEnJhoTNYr=gibPTHMzXiE:FindFirstChild(ldicUNkgp(wENBIFNmVYG[37]))if not bwEnJhoTNYr then return cKqERVmgEW and{}or TolLyuZYkiO end;local CUeivVbvDtTkh=bwEnJhoTNYr:FindFirstChild(ldicUNkgp(wENBIFNmVYG[38]),true)if not CUeivVbvDtTkh then return cKqERVmgEW and{}or TolLyuZYkiO end;CUeivVbvDtTkh=CUeivVbvDtTkh.Parent;local vxJbZYBnvMiAB={}for QojxQJFPvfV,IhVybRqYh in wlcyhgzoPht(4,CUeivVbvDtTkh:GetChildren())do local KehbtMFRgb=IhVybRqYh:FindFirstChild(ldicUNkgp(wENBIFNmVYG[39]))if KehbtMFRgb then local TiUklBcYC=KehbtMFRgb:FindFirstChild(ldicUNkgp(wENBIFNmVYG[40]))if TiUklBcYC then local PIROoJiDcjIRi=wlcyhgzoPht(5,TiUklBcYC.Text:match(ldicUNkgp(wENBIFNmVYG[41])))or 0;if cKqERVmgEW then if PIROoJiDcjIRi>0 then vxJbZYBnvMiAB[IhVybRqYh.Name]=PIROoJiDcjIRi end else TolLyuZYkiO[IhVybRqYh.Name]=PIROoJiDcjIRi end end end end;return cKqERVmgEW and vxJbZYBnvMiAB or TolLyuZYkiO end;local function tNeDqvigm(JpYymDoUQ)local WdJLiYBZrLNPg=QIkHhexdgKcT:FindFirstChild(ldicUNkgp(wENBIFNmVYG[42]))if WdJLiYBZrLNPg then WdJLiYBZrLNPg:FireServer(JpYymDoUQ)end end;local function SWdXXrcYsAdB(cKqERVmgEW)local VrROUWzwRXkQ=gibPTHMzXiE:FindFirstChild(ldicUNkgp(wENBIFNmVYG[43]))if not VrROUWzwRXkQ then return cKqERVmgEW and{}or asozHApzVE end;local CUeivVbvDtTkh=VrROUWzwRXkQ:FindFirstChild(ldicUNkgp(wENBIFNmVYG[44]))if not CUeivVbvDtTkh then for QojxQJFPvfV,iQoDiFIbkg in wlcyhgzoPht(4,VrROUWzwRXkQ:GetDescendants())do if iQoDiFIbkg:IsA(ldicUNkgp(wENBIFNmVYG[45]))and iQoDiFIbkg:FindFirstChild(ldicUNkgp(wENBIFNmVYG[46]))then CUeivVbvDtTkh=iQoDiFIbkg.Parent;break end end end;if not CUeivVbvDtTkh then return cKqERVmgEW and{}or asozHApzVE end;local vxJbZYBnvMiAB={}for QojxQJFPvfV,IhVybRqYh in wlcyhgzoPht(4,CUeivVbvDtTkh:GetChildren())do local KehbtMFRgb=IhVybRqYh:FindFirstChild(ldicUNkgp(wENBIFNmVYG[47]))if KehbtMFRgb then local TiUklBcYC=KehbtMFRgb:FindFirstChild(ldicUNkgp(wENBIFNmVYG[48]))if TiUklBcYC then local PIROoJiDcjIRi=wlcyhgzoPht(5,TiUklBcYC.Text:match(ldicUNkgp(wENBIFNmVYG[49])))or 0;if cKqERVmgEW then if PIROoJiDcjIRi>0 then vxJbZYBnvMiAB[IhVybRqYh.Name]=PIROoJiDcjIRi end else asozHApzVE[IhVybRqYh.Name]=PIROoJiDcjIRi end end end end;return cKqERVmgEW and vxJbZYBnvMiAB or asozHApzVE end;local function vOOHIGAJbUlf(inHrvrtEhDG)local WdJLiYBZrLNPg=QIkHhexdgKcT:FindFirstChild(ldicUNkgp(wENBIFNmVYG[50]))if WdJLiYBZrLNPg then WdJLiYBZrLNPg:FireServer(inHrvrtEhDG)end end;local function nejWDXgpuCRDA(cKqERVmgEW)local gkywfSPJipRP=gibPTHMzXiE:FindFirstChild(ldicUNkgp(wENBIFNmVYG[51]))or gibPTHMzXiE:FindFirstChild(ldicUNkgp(wENBIFNmVYG[52]))or gibPTHMzXiE:FindFirstChild(ldicUNkgp(wENBIFNmVYG[53]))or gibPTHMzXiE:FindFirstChild(ldicUNkgp(wENBIFNmVYG[54]))or gibPTHMzXiE:FindFirstChild(ldicUNkgp(wENBIFNmVYG[55]))if not gkywfSPJipRP then return cKqERVmgEW and{}or sDqsfHdREv end;local CUeivVbvDtTkh=nil;for QojxQJFPvfV,iQoDiFIbkg in wlcyhgzoPht(4,gkywfSPJipRP:GetDescendants())do if iQoDiFIbkg:IsA(ldicUNkgp(wENBIFNmVYG[56]))and iQoDiFIbkg:FindFirstChild(ldicUNkgp(wENBIFNmVYG[57]))then CUeivVbvDtTkh=iQoDiFIbkg.Parent;break end end;if not CUeivVbvDtTkh then for QojxQJFPvfV,iQoDiFIbkg in wlcyhgzoPht(4,gkywfSPJipRP:GetDescendants())do if iQoDiFIbkg:IsA(ldicUNkgp(wENBIFNmVYG[58]))or iQoDiFIbkg:IsA(ldicUNkgp(wENBIFNmVYG[59]))then local VppxmfJbFdt=0;for QojxQJFPvfV,RxYupfZoIDM in wlcyhgzoPht(4,iQoDiFIbkg:GetChildren())do if RxYupfZoIDM:IsA(ldicUNkgp(wENBIFNmVYG[60]))then VppxmfJbFdt=VppxmfJbFdt+1 end end;if VppxmfJbFdt>1 then CUeivVbvDtTkh=iQoDiFIbkg;break end end end end;if not CUeivVbvDtTkh then return cKqERVmgEW and{}or sDqsfHdREv end;local vxJbZYBnvMiAB={}for QojxQJFPvfV,IhVybRqYh in wlcyhgzoPht(4,CUeivVbvDtTkh:GetChildren())do if IhVybRqYh:IsA(ldicUNkgp(wENBIFNmVYG[61]))then local KehbtMFRgb=IhVybRqYh:FindFirstChild(ldicUNkgp(wENBIFNmVYG[62]))if KehbtMFRgb then local TiUklBcYC=KehbtMFRgb:FindFirstChild(ldicUNkgp(wENBIFNmVYG[63]))if TiUklBcYC then local PIROoJiDcjIRi=wlcyhgzoPht(5,TiUklBcYC.Text:match(ldicUNkgp(wENBIFNmVYG[64])))or 0;if cKqERVmgEW then if PIROoJiDcjIRi>0 then vxJbZYBnvMiAB[IhVybRqYh.Name]=PIROoJiDcjIRi end else sDqsfHdREv[IhVybRqYh.Name]=PIROoJiDcjIRi end end end end end;return cKqERVmgEW and vxJbZYBnvMiAB or sDqsfHdREv end;local function ibWDuxPvVGcY(ZQKwDQBB)local lcQMoXcx={ldicUNkgp(wENBIFNmVYG[65]),ldicUNkgp(wENBIFNmVYG[66]),ldicUNkgp(wENBIFNmVYG[67]),ldicUNkgp(wENBIFNmVYG[68]),ldicUNkgp(wENBIFNmVYG[69]),ldicUNkgp(wENBIFNmVYG[70]),ldicUNkgp(wENBIFNmVYG[71]),ldicUNkgp(wENBIFNmVYG[72])}for QojxQJFPvfV,LcUJzxpBr in wlcyhgzoPht(4,lcQMoXcx)do local iXdifnVlP=QIkHhexdgKcT:FindFirstChild(LcUJzxpBr)if iXdifnVlP and iXdifnVlP:IsA(ldicUNkgp(wENBIFNmVYG[73]))then iXdifnVlP:FireServer(ZQKwDQBB)break end end end;local function oPNhHnWAV()local pQKVOeUlzA=AZJbGxoNBa.Selected;if wlcyhgzoPht(1,pQKVOeUlzA)~=ldicUNkgp(wENBIFNmVYG[74])then return end;local MABnJHHg={}for eHPuRdOnjhYR,rcVfbWQXvWxFg in wlcyhgzoPht(4,pQKVOeUlzA)do if rcVfbWQXvWxFg==true or wlcyhgzoPht(1,rcVfbWQXvWxFg)==ldicUNkgp(wENBIFNmVYG[75])and rcVfbWQXvWxFg~=atMZsVpq(TgjuaeTTO[192])then wlcyhgzoPht(3,MABnJHHg,wlcyhgzoPht(1,eHPuRdOnjhYR)==ldicUNkgp(wENBIFNmVYG[76])and eHPuRdOnjhYR or rcVfbWQXvWxFg)elseif wlcyhgzoPht(1,eHPuRdOnjhYR)==ldicUNkgp(wENBIFNmVYG[77])and wlcyhgzoPht(1,rcVfbWQXvWxFg)==ldicUNkgp(wENBIFNmVYG[78])and rcVfbWQXvWxFg~=atMZsVpq(TgjuaeTTO[192])then wlcyhgzoPht(3,MABnJHHg,rcVfbWQXvWxFg)end end;if#MABnJHHg==0 then return end;nejWDXgpuCRDA()for QojxQJFPvfV,cuzYzYtr in wlcyhgzoPht(4,MABnJHHg)do local FIjUctJy=sDqsfHdREv[cuzYzYtr]if FIjUctJy and FIjUctJy>0 then for FRnlCqqbwh=1,FIjUctJy do ibWDuxPvVGcY(cuzYzYtr)end end end end;local function gpXtkuaufOhc()local MhisIktJ=rgQcqeJNtWWy.Selected;if wlcyhgzoPht(1,MhisIktJ)~=ldicUNkgp(wENBIFNmVYG[79])then return end;local nvtcSKEFIlh={}for eHPuRdOnjhYR,rcVfbWQXvWxFg in wlcyhgzoPht(4,MhisIktJ)do if rcVfbWQXvWxFg==true or wlcyhgzoPht(1,rcVfbWQXvWxFg)==ldicUNkgp(wENBIFNmVYG[80])and rcVfbWQXvWxFg~=atMZsVpq(TgjuaeTTO[192])then wlcyhgzoPht(3,nvtcSKEFIlh,wlcyhgzoPht(1,eHPuRdOnjhYR)==ldicUNkgp(wENBIFNmVYG[81])and eHPuRdOnjhYR or rcVfbWQXvWxFg)elseif wlcyhgzoPht(1,eHPuRdOnjhYR)==ldicUNkgp(wENBIFNmVYG[82])and wlcyhgzoPht(1,rcVfbWQXvWxFg)==ldicUNkgp(wENBIFNmVYG[83])and rcVfbWQXvWxFg~=atMZsVpq(TgjuaeTTO[192])then wlcyhgzoPht(3,nvtcSKEFIlh,rcVfbWQXvWxFg)end end;if#nvtcSKEFIlh==0 then return end;SWdXXrcYsAdB()for QojxQJFPvfV,GAIJPxKIrV in wlcyhgzoPht(4,nvtcSKEFIlh)do local FIjUctJy=asozHApzVE[GAIJPxKIrV]if FIjUctJy and FIjUctJy>0 then for FRnlCqqbwh=1,FIjUctJy do vOOHIGAJbUlf(GAIJPxKIrV)end end end end;local function zlnUtfmBFqJZ()local BjJxIBsKTlmBz=yNxBwQplyPk.Selected;if wlcyhgzoPht(1,BjJxIBsKTlmBz)~=ldicUNkgp(wENBIFNmVYG[84])then return end;local JdhfMKCUn={}for eHPuRdOnjhYR,rcVfbWQXvWxFg in wlcyhgzoPht(4,BjJxIBsKTlmBz)do if rcVfbWQXvWxFg==true or wlcyhgzoPht(1,rcVfbWQXvWxFg)==ldicUNkgp(wENBIFNmVYG[85])and rcVfbWQXvWxFg~=atMZsVpq(TgjuaeTTO[192])then wlcyhgzoPht(3,JdhfMKCUn,wlcyhgzoPht(1,eHPuRdOnjhYR)==ldicUNkgp(wENBIFNmVYG[86])and eHPuRdOnjhYR or rcVfbWQXvWxFg)elseif wlcyhgzoPht(1,eHPuRdOnjhYR)==ldicUNkgp(wENBIFNmVYG[87])and wlcyhgzoPht(1,rcVfbWQXvWxFg)==ldicUNkgp(wENBIFNmVYG[88])and rcVfbWQXvWxFg~=atMZsVpq(TgjuaeTTO[192])then wlcyhgzoPht(3,JdhfMKCUn,rcVfbWQXvWxFg)end end;if#JdhfMKCUn==0 then return end;hazHOaWaA()for QojxQJFPvfV,CQiplBLPIAXB in wlcyhgzoPht(4,JdhfMKCUn)do local FIjUctJy=TolLyuZYkiO[CQiplBLPIAXB]if FIjUctJy and FIjUctJy>0 then for FRnlCqqbwh=1,FIjUctJy do tNeDqvigm(CQiplBLPIAXB)end end end end;local function LImpSBhgKPzr()aeMUxmEZ=vSdFyTGSxxZf.Heartbeat:Connect(function()if not lzvOhMYMW or not yNxBwQplyPk.Selected then return end;local BjJxIBsKTlmBz=yNxBwQplyPk.Selected;if wlcyhgzoPht(1,BjJxIBsKTlmBz)~=ldicUNkgp(wENBIFNmVYG[89])then return end;local GSErUwvABW=false;for eHPuRdOnjhYR,rcVfbWQXvWxFg in wlcyhgzoPht(4,BjJxIBsKTlmBz)do if rcVfbWQXvWxFg==true or wlcyhgzoPht(1,rcVfbWQXvWxFg)==ldicUNkgp(wENBIFNmVYG[90])and rcVfbWQXvWxFg~=atMZsVpq(TgjuaeTTO[192])then GSErUwvABW=true;break end end;if not GSErUwvABW then return end;local eyUZBigxOH=hazHOaWaA()local mjdLReuBOso=false;local dgZHePVx={}for eHPuRdOnjhYR,rcVfbWQXvWxFg in wlcyhgzoPht(4,BjJxIBsKTlmBz)do if rcVfbWQXvWxFg==true or wlcyhgzoPht(1,rcVfbWQXvWxFg)==ldicUNkgp(wENBIFNmVYG[91])and rcVfbWQXvWxFg~=atMZsVpq(TgjuaeTTO[192])then local CQiplBLPIAXB=wlcyhgzoPht(1,eHPuRdOnjhYR)==ldicUNkgp(wENBIFNmVYG[92])and eHPuRdOnjhYR or rcVfbWQXvWxFg;if wlcyhgzoPht(1,CQiplBLPIAXB)==ldicUNkgp(wENBIFNmVYG[93])and CQiplBLPIAXB~=atMZsVpq(TgjuaeTTO[192])then wlcyhgzoPht(3,dgZHePVx,CQiplBLPIAXB)end elseif wlcyhgzoPht(1,eHPuRdOnjhYR)==ldicUNkgp(wENBIFNmVYG[94])and wlcyhgzoPht(1,rcVfbWQXvWxFg)==ldicUNkgp(wENBIFNmVYG[95])and rcVfbWQXvWxFg~=atMZsVpq(TgjuaeTTO[192])then wlcyhgzoPht(3,dgZHePVx,rcVfbWQXvWxFg)end end;for QojxQJFPvfV,CQiplBLPIAXB in wlcyhgzoPht(4,dgZHePVx)do local YfDXjliWq=vklotZGB[CQiplBLPIAXB]or-1;local VcsuPzJXoT=eyUZBigxOH[CQiplBLPIAXB]or 0;if YfDXjliWq~=VcsuPzJXoT then vklotZGB[CQiplBLPIAXB]=VcsuPzJXoT;if VcsuPzJXoT>0 then mjdLReuBOso=true end end end;if mjdLReuBOso then zlnUtfmBFqJZ()end end)end;local function xdlIhEyYPQF()if aeMUxmEZ then aeMUxmEZ:Disconnect()aeMUxmEZ=nil end end;local function XhBGrHGuWH()SvCmJlZO=vSdFyTGSxxZf.Heartbeat:Connect(function()if not pHwszcccLmjO or not rgQcqeJNtWWy.Selected then return end;local MhisIktJ=rgQcqeJNtWWy.Selected;if wlcyhgzoPht(1,MhisIktJ)~=ldicUNkgp(wENBIFNmVYG[96])then return end;local GNCcJBVeP=false;for eHPuRdOnjhYR,rcVfbWQXvWxFg in wlcyhgzoPht(4,MhisIktJ)do if rcVfbWQXvWxFg==true or wlcyhgzoPht(1,rcVfbWQXvWxFg)==ldicUNkgp(wENBIFNmVYG[97])and rcVfbWQXvWxFg~=atMZsVpq(TgjuaeTTO[192])then GNCcJBVeP=true;break end end;if not GNCcJBVeP then return end;local eyUZBigxOH=SWdXXrcYsAdB()local mjdLReuBOso=false;local TIoPltQJw={}for eHPuRdOnjhYR,rcVfbWQXvWxFg in wlcyhgzoPht(4,MhisIktJ)do if rcVfbWQXvWxFg==true or wlcyhgzoPht(1,rcVfbWQXvWxFg)==ldicUNkgp(wENBIFNmVYG[98])and rcVfbWQXvWxFg~=atMZsVpq(TgjuaeTTO[192])then local GAIJPxKIrV=wlcyhgzoPht(1,eHPuRdOnjhYR)==ldicUNkgp(wENBIFNmVYG[99])and eHPuRdOnjhYR or rcVfbWQXvWxFg;if wlcyhgzoPht(1,GAIJPxKIrV)==ldicUNkgp(wENBIFNmVYG[100])and GAIJPxKIrV~=atMZsVpq(TgjuaeTTO[192])then wlcyhgzoPht(3,TIoPltQJw,GAIJPxKIrV)end elseif wlcyhgzoPht(1,eHPuRdOnjhYR)==ldicUNkgp(wENBIFNmVYG[101])and wlcyhgzoPht(1,rcVfbWQXvWxFg)==ldicUNkgp(wENBIFNmVYG[102])and rcVfbWQXvWxFg~=atMZsVpq(TgjuaeTTO[192])then wlcyhgzoPht(3,TIoPltQJw,rcVfbWQXvWxFg)end end;for QojxQJFPvfV,GAIJPxKIrV in wlcyhgzoPht(4,TIoPltQJw)do local YfDXjliWq=OkRFSdRIfviRs[GAIJPxKIrV]or-1;local VcsuPzJXoT=eyUZBigxOH[GAIJPxKIrV]or 0;if YfDXjliWq~=VcsuPzJXoT then OkRFSdRIfviRs[GAIJPxKIrV]=VcsuPzJXoT;if VcsuPzJXoT>0 then mjdLReuBOso=true end end end;if mjdLReuBOso then gpXtkuaufOhc()end end)end;local function eGLhYAPvKKRog()if SvCmJlZO then SvCmJlZO:Disconnect()SvCmJlZO=nil end end;local function xlsqcHStEc()bzSGfrfv=vSdFyTGSxxZf.Heartbeat:Connect(function()if not UzBMBDeWlnIJ or not AZJbGxoNBa.Selected then return end;local pQKVOeUlzA=AZJbGxoNBa.Selected;if wlcyhgzoPht(1,pQKVOeUlzA)~=ldicUNkgp(wENBIFNmVYG[103])then return end;local KNnnyvjfKOTn=false;for eHPuRdOnjhYR,rcVfbWQXvWxFg in wlcyhgzoPht(4,pQKVOeUlzA)do if rcVfbWQXvWxFg==true or wlcyhgzoPht(1,rcVfbWQXvWxFg)==ldicUNkgp(wENBIFNmVYG[104])and rcVfbWQXvWxFg~=atMZsVpq(TgjuaeTTO[192])then KNnnyvjfKOTn=true;break end end;if not KNnnyvjfKOTn then return end;local eyUZBigxOH=nejWDXgpuCRDA()local mjdLReuBOso=false;local vlvgyCzymtR={}for eHPuRdOnjhYR,rcVfbWQXvWxFg in wlcyhgzoPht(4,pQKVOeUlzA)do if rcVfbWQXvWxFg==true or wlcyhgzoPht(1,rcVfbWQXvWxFg)==ldicUNkgp(wENBIFNmVYG[105])and rcVfbWQXvWxFg~=atMZsVpq(TgjuaeTTO[192])then local cuzYzYtr=wlcyhgzoPht(1,eHPuRdOnjhYR)==ldicUNkgp(wENBIFNmVYG[106])and eHPuRdOnjhYR or rcVfbWQXvWxFg;if wlcyhgzoPht(1,cuzYzYtr)==ldicUNkgp(wENBIFNmVYG[107])and cuzYzYtr~=atMZsVpq(TgjuaeTTO[192])then wlcyhgzoPht(3,vlvgyCzymtR,cuzYzYtr)end elseif wlcyhgzoPht(1,eHPuRdOnjhYR)==ldicUNkgp(wENBIFNmVYG[108])and wlcyhgzoPht(1,rcVfbWQXvWxFg)==ldicUNkgp(wENBIFNmVYG[109])and rcVfbWQXvWxFg~=atMZsVpq(TgjuaeTTO[192])then wlcyhgzoPht(3,vlvgyCzymtR,rcVfbWQXvWxFg)end end;for QojxQJFPvfV,cuzYzYtr in wlcyhgzoPht(4,vlvgyCzymtR)do local YfDXjliWq=GXFXwCdZvVtYe[cuzYzYtr]or-1;local VcsuPzJXoT=eyUZBigxOH[cuzYzYtr]or 0;if YfDXjliWq~=VcsuPzJXoT then GXFXwCdZvVtYe[cuzYzYtr]=VcsuPzJXoT;if VcsuPzJXoT>0 then mjdLReuBOso=true end end end;if mjdLReuBOso then oPNhHnWAV()end end)end;local function ZuxRkvWEVk()if bzSGfrfv then bzSGfrfv:Disconnect()bzSGfrfv=nil end end;local NOydkULp=dJlhVFCyxRvES:CreateWindow({Name=ldicUNkgp(wENBIFNmVYG[110])..1.0,LoadingTitle=ldicUNkgp(wENBIFNmVYG[111]),LoadingSubtitle=ldicUNkgp(wENBIFNmVYG[112]),ConfigurationSaving={Enabled=true,FolderName=ldicUNkgp(wENBIFNmVYG[113]),FileName=ldicUNkgp(wENBIFNmVYG[114])},Discord={Enabled=false,Invite=ldicUNkgp(wENBIFNmVYG[115]),RememberJoins=true},KeySystem=false,KeySettings={Title=ldicUNkgp(wENBIFNmVYG[116]),Subtitle=ldicUNkgp(wENBIFNmVYG[117]),Note=ldicUNkgp(wENBIFNmVYG[118]),FileName=ldicUNkgp(wENBIFNmVYG[119]),SaveKey=true,GrabKeyFromSite=false,Key={ldicUNkgp(wENBIFNmVYG[120])}}})local CpWiFuQk={Main=NOydkULp:CreateTab(ldicUNkgp(wENBIFNmVYG[121]),4483362458),Seed=NOydkULp:CreateTab(ldicUNkgp(wENBIFNmVYG[122]),4483362458),Gear=NOydkULp:CreateTab(ldicUNkgp(wENBIFNmVYG[123]),4483362458),Egg=NOydkULp:CreateTab(ldicUNkgp(wENBIFNmVYG[124]),4483362458)}local aXGcZEyYngn=CpWiFuQk.Main:CreateSlider({Name=ldicUNkgp(wENBIFNmVYG[125]),Range={16,200},Increment=1,Suffix=atMZsVpq(TgjuaeTTO[192]),CurrentValue=16,Flag=ldicUNkgp(wENBIFNmVYG[126]),Callback=function(PKGJSEZwRGwo)local cdWMceqLVO=game.Players.LocalPlayer;local VbQYqnCbNhb=cdWMceqLVO.Character;if VbQYqnCbNhb and VbQYqnCbNhb:FindFirstChild(ldicUNkgp(wENBIFNmVYG[127]))then VbQYqnCbNhb.Humanoid.WalkSpeed=PKGJSEZwRGwo end end})local tCRQRTmPQ=CpWiFuQk.Main:CreateSlider({Name=ldicUNkgp(wENBIFNmVYG[128]),Range={50,200},Increment=1,Suffix=atMZsVpq(TgjuaeTTO[192]),CurrentValue=50,Flag=ldicUNkgp(wENBIFNmVYG[129]),Callback=function(PKGJSEZwRGwo)local cdWMceqLVO=game.Players.LocalPlayer;local VbQYqnCbNhb=cdWMceqLVO.Character;if VbQYqnCbNhb and VbQYqnCbNhb:FindFirstChild(ldicUNkgp(wENBIFNmVYG[130]))then VbQYqnCbNhb.Humanoid.JumpPower=PKGJSEZwRGwo end end})local vloiksrVs=false;local mBgnSiIyVufU=nil;local zCtmkEQc=nil;local OobMEZPg=nil;local BhjnQEomii=50;local cHljWkFT=nil;local acTPkDQRuH=nil;local fxZHXxRtCZuCK=nil;local eKxdUgwMv=jJtYcAsF.TouchEnabled;local gRYBCTDGX=Color3.fromRGB(0,162,255)local JSYqOWXOf=Color3.fromRGB(255,87,87)local LkhQIEbAFxc=Color3.fromRGB(255,255,255)local function rzfvBNMeDuuo()if not eKxdUgwMv then return end;local rDotLcHbJq=Instance.new(ldicUNkgp(wENBIFNmVYG[131]))rDotLcHbJq.Name=ldicUNkgp(wENBIFNmVYG[132])rDotLcHbJq.Parent=YmXScPHf.PlayerGui;rDotLcHbJq.ResetOnSpawn=false;fxZHXxRtCZuCK=Instance.new(ldicUNkgp(wENBIFNmVYG[133]))fxZHXxRtCZuCK.Name=ldicUNkgp(wENBIFNmVYG[134])fxZHXxRtCZuCK.Size=UDim2.new(0,120,0,200)fxZHXxRtCZuCK.Position=UDim2.new(1,-130,0.5,-100)fxZHXxRtCZuCK.BackgroundTransparency=1;fxZHXxRtCZuCK.Parent=rDotLcHbJq;cHljWkFT=Instance.new(ldicUNkgp(wENBIFNmVYG[135]))cHljWkFT.Name=ldicUNkgp(wENBIFNmVYG[136])cHljWkFT.Size=UDim2.new(0,100,0,80)cHljWkFT.Position=UDim2.new(0,10,0,10)cHljWkFT.BackgroundColor3=gRYBCTDGX;cHljWkFT.BorderSizePixel=0;cHljWkFT.Text=atMZsVpq(TgjuaeTTO[195])cHljWkFT.TextColor3=LkhQIEbAFxc;cHljWkFT.TextScaled=true;cHljWkFT.Font=Enum.Font.GothamBold;cHljWkFT.Parent=fxZHXxRtCZuCK;local ixRFMacUF=Instance.new(ldicUNkgp(wENBIFNmVYG[137]))ixRFMacUF.CornerRadius=UDim.new(0,10)ixRFMacUF.Parent=cHljWkFT;acTPkDQRuH=Instance.new(ldicUNkgp(wENBIFNmVYG[138]))acTPkDQRuH.Name=ldicUNkgp(wENBIFNmVYG[139])acTPkDQRuH.Size=UDim2.new(0,100,0,80)acTPkDQRuH.Position=UDim2.new(0,10,0,110)acTPkDQRuH.BackgroundColor3=JSYqOWXOf;acTPkDQRuH.BorderSizePixel=0;acTPkDQRuH.Text=ldicUNkgp(wENBIFNmVYG[140])acTPkDQRuH.TextColor3=LkhQIEbAFxc;acTPkDQRuH.TextScaled=true;acTPkDQRuH.Font=Enum.Font.GothamBold;acTPkDQRuH.Parent=fxZHXxRtCZuCK;local CwmOlIES=Instance.new(ldicUNkgp(wENBIFNmVYG[141]))CwmOlIES.CornerRadius=UDim.new(0,10)CwmOlIES.Parent=acTPkDQRuH end;local function TOGgXlYJH()if fxZHXxRtCZuCK and fxZHXxRtCZuCK.Parent then fxZHXxRtCZuCK.Parent:Destroy()end;cHljWkFT=nil;acTPkDQRuH=nil;fxZHXxRtCZuCK=nil end;local function XPpzJQUP()local cdWMceqLVO=game.Players.LocalPlayer;local VbQYqnCbNhb=cdWMceqLVO.Character;if not VbQYqnCbNhb or not VbQYqnCbNhb:FindFirstChild(ldicUNkgp(wENBIFNmVYG[142]))then return end;local piDhDVuxj=VbQYqnCbNhb.HumanoidRootPart;mBgnSiIyVufU=Instance.new(ldicUNkgp(wENBIFNmVYG[143]))mBgnSiIyVufU.MaxForce=Vector3.new(9e9,9e9,9e9)mBgnSiIyVufU.Velocity=Vector3.new(0,0,0)mBgnSiIyVufU.Parent=piDhDVuxj;zCtmkEQc=Instance.new(ldicUNkgp(wENBIFNmVYG[144]))zCtmkEQc.MaxTorque=Vector3.new(0,9e9,0)zCtmkEQc.AngularVelocity=Vector3.new(0,0,0)zCtmkEQc.Parent=piDhDVuxj;rzfvBNMeDuuo()OobMEZPg=vSdFyTGSxxZf.Heartbeat:Connect(function()local JNPCHibDXAFN=Vector3.new(0,0,0)local PwItMkLWMz=dCmrgvbAGBji.CFrame.LookVector;local tyuTXXGmcGrW=dCmrgvbAGBji.CFrame.RightVector;local GKOUceMQm=Vector3.new(0,1,0)if not eKxdUgwMv then if jJtYcAsF:IsKeyDown(Enum.KeyCode.W)then JNPCHibDXAFN=JNPCHibDXAFN+PwItMkLWMz*BhjnQEomii end;if jJtYcAsF:IsKeyDown(Enum.KeyCode.S)then JNPCHibDXAFN=JNPCHibDXAFN-PwItMkLWMz*BhjnQEomii end;if jJtYcAsF:IsKeyDown(Enum.KeyCode.A)then JNPCHibDXAFN=JNPCHibDXAFN-tyuTXXGmcGrW*BhjnQEomii end;if jJtYcAsF:IsKeyDown(Enum.KeyCode.D)then JNPCHibDXAFN=JNPCHibDXAFN+tyuTXXGmcGrW*BhjnQEomii end;if jJtYcAsF:IsKeyDown(Enum.KeyCode.Space)then JNPCHibDXAFN=JNPCHibDXAFN+GKOUceMQm*BhjnQEomii end;if jJtYcAsF:IsKeyDown(Enum.KeyCode.LeftShift)then JNPCHibDXAFN=JNPCHibDXAFN-GKOUceMQm*BhjnQEomii end else local roOGSRbVqm=VbQYqnCbNhb.Humanoid.MoveDirection;if roOGSRbVqm.Magnitude>0 then local FWicfgZMoTUn=dCmrgvbAGBji.CFrame;local HUMpRvXc=FWicfgZMoTUn:VectorToWorldSpace(Vector3.new(roOGSRbVqm.X,0,-roOGSRbVqm.Z))JNPCHibDXAFN=JNPCHibDXAFN+HUMpRvXc*BhjnQEomii end;if cHljWkFT and cHljWkFT.Parent then if not cHljWkFT:GetAttribute(ldicUNkgp(wENBIFNmVYG[145]))then cHljWkFT:SetAttribute(ldicUNkgp(wENBIFNmVYG[146]),true)cHljWkFT.MouseButton1Down:Connect(function()cHljWkFT:SetAttribute(ldicUNkgp(wENBIFNmVYG[147]),true)end)cHljWkFT.MouseButton1Up:Connect(function()cHljWkFT:SetAttribute(ldicUNkgp(wENBIFNmVYG[148]),false)end)end;if acTPkDQRuH and not acTPkDQRuH:GetAttribute(ldicUNkgp(wENBIFNmVYG[149]))then acTPkDQRuH:SetAttribute(ldicUNkgp(wENBIFNmVYG[150]),true)acTPkDQRuH.MouseButton1Down:Connect(function()acTPkDQRuH:SetAttribute(ldicUNkgp(wENBIFNmVYG[151]),true)end)acTPkDQRuH.MouseButton1Up:Connect(function()acTPkDQRuH:SetAttribute(ldicUNkgp(wENBIFNmVYG[152]),false)end)end;if cHljWkFT:GetAttribute(ldicUNkgp(wENBIFNmVYG[153]))then JNPCHibDXAFN=JNPCHibDXAFN+GKOUceMQm*BhjnQEomii end;if acTPkDQRuH and acTPkDQRuH:GetAttribute(ldicUNkgp(wENBIFNmVYG[154]))then JNPCHibDXAFN=JNPCHibDXAFN-GKOUceMQm*BhjnQEomii end end end;mBgnSiIyVufU.Velocity=JNPCHibDXAFN end)end;local function kAaESdKVds()if mBgnSiIyVufU then mBgnSiIyVufU:Destroy()mBgnSiIyVufU=nil end;if zCtmkEQc then zCtmkEQc:Destroy()zCtmkEQc=nil end;if OobMEZPg then OobMEZPg:Disconnect()OobMEZPg=nil end;TOGgXlYJH()end;local eAOeOcqZqmb=CpWiFuQk.Main:CreateToggle({Name=ldicUNkgp(wENBIFNmVYG[155]),CurrentValue=false,Flag=ldicUNkgp(wENBIFNmVYG[156]),Callback=function(PKGJSEZwRGwo)vloiksrVs=PKGJSEZwRGwo;if vloiksrVs then XPpzJQUP()else kAaESdKVds()end end})local DvgrxFIkDArlH=CpWiFuQk.Main:CreateToggle({Name=ldicUNkgp(wENBIFNmVYG[157]),CurrentValue=false,Flag=ldicUNkgp(wENBIFNmVYG[158]),Callback=function(PKGJSEZwRGwo)nOxwHHSKgoM=PKGJSEZwRGwo;MFPLcvKloHFx(ldicUNkgp(wENBIFNmVYG[159]),PKGJSEZwRGwo)if nOxwHHSKgoM then LAZwEwRhlVvbU()else KjbbDgBy()end end})local dFVHnvFtJk=CpWiFuQk.Main:CreateButton({Name=ldicUNkgp(wENBIFNmVYG[160]),Callback=function()local cdWMceqLVO=game.Players.LocalPlayer;if cdWMceqLVO.Character then cdWMceqLVO.Character:BreakJoints()end end})local VhXkvDTKFo=CpWiFuQk.Seed:CreateDropdown({Name=ldicUNkgp(wENBIFNmVYG[161]),Options={},CurrentOption={},MultipleOptions=true,Flag=ldicUNkgp(wENBIFNmVYG[162]),Callback=function(PKGJSEZwRGwo)yNxBwQplyPk.Selected=PKGJSEZwRGwo;MFPLcvKloHFx(ldicUNkgp(wENBIFNmVYG[163]),PKGJSEZwRGwo)hazHOaWaA()end})local function LalNjTTXIrb()local nvqFXQGn=hazHOaWaA(false)local aqwKzhZEwGn={}for CQiplBLPIAXB,QojxQJFPvfV in wlcyhgzoPht(4,nvqFXQGn)do wlcyhgzoPht(3,aqwKzhZEwGn,CQiplBLPIAXB)end;VhXkvDTKFo:Refresh(aqwKzhZEwGn,true)end;local xbjFHShYGVk=CpWiFuQk.Seed:CreateToggle({Name=ldicUNkgp(wENBIFNmVYG[164]),CurrentValue=false,Flag=ldicUNkgp(wENBIFNmVYG[165]),Callback=function(PKGJSEZwRGwo)lzvOhMYMW=PKGJSEZwRGwo;MFPLcvKloHFx(ldicUNkgp(wENBIFNmVYG[166]),PKGJSEZwRGwo)if lzvOhMYMW then LImpSBhgKPzr()else xdlIhEyYPQF()end end})LalNjTTXIrb()local enAhdUHux=CpWiFuQk.Gear:CreateDropdown({Name=ldicUNkgp(wENBIFNmVYG[167]),Options={},CurrentOption={},MultipleOptions=true,Flag=ldicUNkgp(wENBIFNmVYG[168]),Callback=function(PKGJSEZwRGwo)rgQcqeJNtWWy.Selected=PKGJSEZwRGwo;MFPLcvKloHFx(ldicUNkgp(wENBIFNmVYG[169]),PKGJSEZwRGwo)SWdXXrcYsAdB()end})local function awhCRcpnkDrri()local wIQubKHAgKTrY=SWdXXrcYsAdB(false)local cogUfEVhfG={}for GAIJPxKIrV,QojxQJFPvfV in wlcyhgzoPht(4,wIQubKHAgKTrY)do wlcyhgzoPht(3,cogUfEVhfG,GAIJPxKIrV)end;enAhdUHux:Refresh(cogUfEVhfG,true)end;local EUDqzJUfChU=CpWiFuQk.Gear:CreateToggle({Name=ldicUNkgp(wENBIFNmVYG[170]),CurrentValue=false,Flag=ldicUNkgp(wENBIFNmVYG[171]),Callback=function(PKGJSEZwRGwo)pHwszcccLmjO=PKGJSEZwRGwo;MFPLcvKloHFx(ldicUNkgp(wENBIFNmVYG[172]),PKGJSEZwRGwo)if pHwszcccLmjO then XhBGrHGuWH()else eGLhYAPvKKRog()end end})awhCRcpnkDrri()local ehOlSgWjNPnxK=CpWiFuQk.Egg:CreateDropdown({Name=ldicUNkgp(wENBIFNmVYG[173]),Options={},CurrentOption={},MultipleOptions=true,Flag=ldicUNkgp(wENBIFNmVYG[174]),Callback=function(PKGJSEZwRGwo)AZJbGxoNBa.Selected=PKGJSEZwRGwo;MFPLcvKloHFx(ldicUNkgp(wENBIFNmVYG[175]),PKGJSEZwRGwo)nejWDXgpuCRDA()end})local function nuVmsqcoHuD()local tgCjDdkmy=nejWDXgpuCRDA(false)local orWrTkhJQD={}for cuzYzYtr,QojxQJFPvfV in wlcyhgzoPht(4,tgCjDdkmy)do wlcyhgzoPht(3,orWrTkhJQD,cuzYzYtr)end;ehOlSgWjNPnxK:Refresh(orWrTkhJQD,true)end;local ZDYwnjykR=CpWiFuQk.Egg:CreateToggle({Name=ldicUNkgp(wENBIFNmVYG[176]),CurrentValue=false,Flag=ldicUNkgp(wENBIFNmVYG[177]),Callback=function(PKGJSEZwRGwo)UzBMBDeWlnIJ=PKGJSEZwRGwo;MFPLcvKloHFx(ldicUNkgp(wENBIFNmVYG[178]),PKGJSEZwRGwo)if UzBMBDeWlnIJ then xlsqcHStEc()else ZuxRkvWEVk()end end})nuVmsqcoHuD()local function gjLCAfLI()local tUmaqeoEzLOZ=WIdOSQOk(ldicUNkgp(wENBIFNmVYG[179]),{})if wlcyhgzoPht(1,tUmaqeoEzLOZ)==ldicUNkgp(wENBIFNmVYG[180])and#tUmaqeoEzLOZ>0 then yNxBwQplyPk.Selected=tUmaqeoEzLOZ;VhXkvDTKFo:Set(tUmaqeoEzLOZ)end;local nUOjGQfgUwR=WIdOSQOk(ldicUNkgp(wENBIFNmVYG[181]),{})if wlcyhgzoPht(1,nUOjGQfgUwR)==ldicUNkgp(wENBIFNmVYG[182])and#nUOjGQfgUwR>0 then rgQcqeJNtWWy.Selected=nUOjGQfgUwR;enAhdUHux:Set(nUOjGQfgUwR)end;local lniTfZyl=WIdOSQOk(ldicUNkgp(wENBIFNmVYG[183]),{})if wlcyhgzoPht(1,lniTfZyl)==ldicUNkgp(wENBIFNmVYG[184])and#lniTfZyl>0 then AZJbGxoNBa.Selected=lniTfZyl;ehOlSgWjNPnxK:Set(lniTfZyl)end;local ruTwMeqnJueD=WIdOSQOk(ldicUNkgp(wENBIFNmVYG[185]),false)if ruTwMeqnJueD then xbjFHShYGVk:Set(ruTwMeqnJueD)end;local UXSDbxKo=WIdOSQOk(ldicUNkgp(wENBIFNmVYG[186]),false)if UXSDbxKo then EUDqzJUfChU:Set(UXSDbxKo)end;local kIIFOJbmvxn=WIdOSQOk(ldicUNkgp(wENBIFNmVYG[187]),false)if kIIFOJbmvxn then ZDYwnjykR:Set(kIIFOJbmvxn)end;local rwOOSLfjt=WIdOSQOk(ldicUNkgp(wENBIFNmVYG[188]),false)if rwOOSLfjt then DvgrxFIkDArlH:Set(rwOOSLfjt)end;if#tUmaqeoEzLOZ>0 or#nUOjGQfgUwR>0 or#lniTfZyl>0 or ruTwMeqnJueD or UXSDbxKo or kIIFOJbmvxn or rwOOSLfjt then dJlhVFCyxRvES:Notify({Title=ldicUNkgp(wENBIFNmVYG[189]),Content=ldicUNkgp(wENBIFNmVYG[190]),Duration=2})end end;spawn(function()task.wait(2)gjLCAfLI()end)